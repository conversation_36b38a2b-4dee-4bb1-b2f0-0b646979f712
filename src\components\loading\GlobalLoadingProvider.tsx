'use client';

import React, { createContext, useContext, useState, useEffect, useRef, useCallback, ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import GlobalLoadingSpinner from './GlobalLoadingSpinner';

export interface GlobalLoadingContextType {
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
}

const GlobalLoadingContext = createContext<GlobalLoadingContextType | undefined>(undefined);

interface GlobalLoadingProviderProps {
  children: ReactNode;
}

/**
 * Global Loading Provider
 *
 * Automatically manages loading states for:
 * - Initial page loads
 * - Page refreshes
 * - Route transitions
 * - Manual loading triggers
 *
 * Features:
 * - Automatic detection of page load states
 * - Route change detection
 * - Smooth transitions
 * - Global loading spinner overlay
 */
export function GlobalLoadingProvider({ children }: GlobalLoadingProviderProps): React.JSX.Element {
  const [loadingState, setLoadingState] = useState<'initial' | 'route' | 'manual' | 'hidden'>('initial');
  const [isVisible, setIsVisible] = useState(true);
  const pathname = usePathname();
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const stateTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentPathnameRef = useRef(pathname);

  // Premium loading durations
  const MINIMUM_LOADING_TIME = 1800; // 1.8 seconds for premium feel
  const ROUTE_LOADING_TIME = 1200;   // 1.2 seconds for route changes
  const FADE_OUT_DURATION = 800;     // 0.8 seconds fade out

  // Centralized loading state manager
  const showLoading = useCallback((type: 'initial' | 'route' | 'manual', duration: number) => {
    // Clear any existing timers
    if (loadingTimerRef.current) clearTimeout(loadingTimerRef.current);
    if (stateTimerRef.current) clearTimeout(stateTimerRef.current);

    // Set loading state and ensure visibility
    setLoadingState(type);
    setIsVisible(true);

    // Set minimum display time
    loadingTimerRef.current = setTimeout(() => {
      setIsVisible(false);

      // Hide completely after fade out
      stateTimerRef.current = setTimeout(() => {
        setLoadingState('hidden');
      }, FADE_OUT_DURATION);
    }, duration);
  }, [FADE_OUT_DURATION]);

  // Handle initial page load with premium timing
  useEffect(() => {
    const handleInitialLoad = () => {
      // Ensure we show loading for the premium duration
      showLoading('initial', MINIMUM_LOADING_TIME);
    };

    if (document.readyState === 'complete') {
      // Page already loaded, but still show premium loading
      handleInitialLoad();
    } else {
      // Wait for page to load, then show premium loading
      const onLoad = () => {
        handleInitialLoad();
      };

      window.addEventListener('load', onLoad);
      return () => window.removeEventListener('load', onLoad);
    }
  }, [showLoading, MINIMUM_LOADING_TIME]);

  // Handle route changes with debouncing
  useEffect(() => {
    // Skip if this is the initial pathname or we're still in initial loading
    if (currentPathnameRef.current === pathname || loadingState === 'initial') {
      currentPathnameRef.current = pathname;
      return;
    }

    // Only show route loading if we're not already loading
    if (loadingState === 'hidden') {
      showLoading('route', ROUTE_LOADING_TIME);
    }

    currentPathnameRef.current = pathname;
  }, [pathname, loadingState, showLoading, ROUTE_LOADING_TIME]);

  // Manual loading control
  const setLoading = useCallback((loading: boolean) => {
    if (loading && loadingState === 'hidden') {
      showLoading('manual', MINIMUM_LOADING_TIME);
    } else if (!loading && loadingState === 'manual') {
      setIsVisible(false);
      stateTimerRef.current = setTimeout(() => {
        setLoadingState('hidden');
      }, FADE_OUT_DURATION);
    }
  }, [loadingState, showLoading, MINIMUM_LOADING_TIME, FADE_OUT_DURATION]);

  // Cleanup timers
  useEffect(() => {
    return () => {
      if (loadingTimerRef.current) clearTimeout(loadingTimerRef.current);
      if (stateTimerRef.current) clearTimeout(stateTimerRef.current);
    };
  }, []);

  const contextValue: GlobalLoadingContextType = {
    isLoading: loadingState === 'manual',
    setLoading,
  };

  const shouldShowSpinner = loadingState !== 'hidden';

  return (
    <GlobalLoadingContext.Provider value={contextValue}>
      {shouldShowSpinner && <GlobalLoadingSpinner isVisible={isVisible} />}
      {children}
    </GlobalLoadingContext.Provider>
  );
}

/**
 * Hook to use global loading context
 */
export function useGlobalLoading(): GlobalLoadingContextType {
  const context = useContext(GlobalLoadingContext);
  if (context === undefined) {
    throw new Error('useGlobalLoading must be used within a GlobalLoadingProvider');
  }
  return context;
}

/**
 * Hook for manual loading control
 * Use this when you need to show loading for specific operations
 */
export function useLoadingControl() {
  const { setLoading } = useGlobalLoading();

  const showLoading = () => setLoading(true);
  const hideLoading = () => setLoading(false);

  const withLoading = async <T,>(operation: () => Promise<T>): Promise<T> => {
    showLoading();
    try {
      const result = await operation();
      return result;
    } finally {
      hideLoading();
    }
  };

  return {
    showLoading,
    hideLoading,
    withLoading,
  };
}

'use client';

import React, { useEffect, useState } from 'react';
import LoadingSpinner from './LoadingSpinner';

export interface PageLoaderProps {
  isLoading: boolean;
  message?: string;
  minLoadTime?: number;
  children: React.ReactNode;
}

/**
 * Page Loader Component
 *
 * A full-page loading wrapper that shows the NESA loading spinner
 * while content is loading. Features:
 * - Smooth fade transitions
 * - Minimum loading time to prevent flashing
 * - Custom loading messages
 * - Elegant entrance animations
 *
 * @param isLoading - Whether to show loading state
 * @param message - Custom loading message
 * @param minLoadTime - Minimum time to show loader (prevents flashing)
 * @param children - Content to show when not loading
 * @returns The rendered page loader component
 */
export default function PageLoader({
  isLoading,
  message = 'Loading NESA...',
  minLoadTime = 800,
  children
}: PageLoaderProps): React.JSX.Element {
  const [showLoader, setShowLoader] = useState(isLoading);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    if (!isLoading) {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, minLoadTime - elapsed);

      setTimeout(() => {
        setShowLoader(false);
      }, remaining);
    } else {
      setShowLoader(true);
    }
  }, [isLoading, startTime, minLoadTime]);

  if (showLoader) {
    return (
      <div className="fixed inset-0 z-[9999]">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1A1A1A] via-[#2D1810] to-[#1A1A1A] animate-gradient">
          {/* Subtle Pattern Overlay */}
          <div className="absolute inset-0 opacity-5">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
              }}
            ></div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen p-8">
          <div className="text-center space-y-8 animate-fade-in-up">
            {/* Main Spinner */}
            <LoadingSpinner
              size="xl"
              variant="default"
              message={message}
              showMessage={true}
            />

            {/* Additional Branding */}
            <div className="space-y-4 animate-fade-in" style={{ animationDelay: '0.3s' }}>
              <h1 className="text-2xl md:text-3xl font-bold text-[#D4AF37] tracking-wide">
                NESA
              </h1>
              <p className="text-sm md:text-base text-white/70 max-w-md mx-auto leading-relaxed">
                New Education Standard Award
              </p>

              {/* Progress Bar */}
              <div className="w-64 mx-auto">
                <div className="h-1 bg-white/10 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-[#D4AF37] to-[#F4C430] rounded-full animate-progress"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="animate-fade-in">
      {children}
    </div>
  );
}

// Additional animations for PageLoader
export const pageLoaderAnimations = `
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 8s ease infinite;
}

.animate-progress {
  animation: progress 2s ease-in-out infinite;
}
`;

'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

interface LoadingDebuggerProps {
  enabled?: boolean;
}

/**
 * Loading Debugger Component
 * 
 * Helps debug loading states and timing during development.
 * Shows real-time information about loading triggers and states.
 * 
 * Only renders in development mode when enabled.
 */
export default function LoadingDebugger({ enabled = false }: LoadingDebuggerProps): React.JSX.Element | null {
  const [logs, setLogs] = useState<string[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const pathname = usePathname();

  // Add log entry
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`${timestamp}: ${message}`, ...prev.slice(0, 9)]);
  };

  // Track pathname changes
  useEffect(() => {
    addLog(`Route changed to: ${pathname}`);
  }, [pathname]);

  // Track document ready state
  useEffect(() => {
    addLog(`Document ready state: ${document.readyState}`);
    
    const handleReadyStateChange = () => {
      addLog(`Document ready state changed to: ${document.readyState}`);
    };

    document.addEventListener('readystatechange', handleReadyStateChange);
    return () => document.removeEventListener('readystatechange', handleReadyStateChange);
  }, []);

  // Track page load events
  useEffect(() => {
    const handleLoad = () => addLog('Window load event fired');
    const handleBeforeUnload = () => addLog('Before unload event fired');
    const handleUnload = () => addLog('Unload event fired');

    window.addEventListener('load', handleLoad);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('load', handleLoad);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, []);

  if (!enabled || process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed top-4 right-4 z-[10001] bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg text-xs font-mono transition-colors"
        style={{ fontSize: '10px' }}
      >
        Debug {isVisible ? '✕' : '◐'}
      </button>

      {/* Debug Panel */}
      {isVisible && (
        <div className="fixed top-16 right-4 z-[10001] bg-black/90 backdrop-blur-sm text-white p-4 rounded-lg max-w-sm w-full border border-white/20">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-bold text-yellow-400">Loading Debugger</h3>
            <button
              onClick={() => setLogs([])}
              className="text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded transition-colors"
            >
              Clear
            </button>
          </div>
          
          <div className="space-y-2">
            <div className="text-xs">
              <span className="text-gray-400">Current Path:</span>
              <div className="text-blue-300 font-mono break-all">{pathname}</div>
            </div>
            
            <div className="text-xs">
              <span className="text-gray-400">Document State:</span>
              <div className="text-green-300 font-mono">{document.readyState}</div>
            </div>
            
            <div className="border-t border-white/20 pt-2">
              <div className="text-xs text-gray-400 mb-2">Recent Events:</div>
              <div className="max-h-40 overflow-y-auto space-y-1">
                {logs.length === 0 ? (
                  <div className="text-xs text-gray-500 italic">No events logged yet</div>
                ) : (
                  logs.map((log, index) => (
                    <div
                      key={index}
                      className="text-xs font-mono text-gray-200 bg-gray-800/50 p-1 rounded text-wrap break-all"
                    >
                      {log}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

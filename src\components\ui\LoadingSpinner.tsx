'use client';

import React from 'react';
import Image from 'next/image';

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'overlay' | 'page';
  message?: string;
  showMessage?: boolean;
}

/**
 * NESA Loading Spinner Component
 *
 * A premium loading spinner using the NESA logo with beautiful animations:
 * - Rotating outer ring with golden gradient
 * - Pulsing NESA logo in the center
 * - Smooth fade-in animations
 * - Multiple size variants
 * - Optional loading messages
 * - Overlay and page-level variants
 *
 * @param size - Size variant: 'sm' | 'md' | 'lg' | 'xl'
 * @param variant - Display variant: 'default' | 'overlay' | 'page'
 * @param message - Custom loading message
 * @param showMessage - Whether to show loading message
 * @returns The rendered loading spinner component
 */
export default function LoadingSpinner({
  size = 'md',
  variant = 'default',
  message = 'Loading...',
  showMessage = true
}: LoadingSpinnerProps): React.JSX.Element {

  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'w-12 h-12',
      logo: 'w-8 h-8',
      ring: 'w-12 h-12',
      text: 'text-sm'
    },
    md: {
      container: 'w-16 h-16',
      logo: 'w-10 h-10',
      ring: 'w-16 h-16',
      text: 'text-base'
    },
    lg: {
      container: 'w-24 h-24',
      logo: 'w-16 h-16',
      ring: 'w-24 h-24',
      text: 'text-lg'
    },
    xl: {
      container: 'w-32 h-32',
      logo: 'w-20 h-20',
      ring: 'w-32 h-32',
      text: 'text-xl'
    }
  };

  const config = sizeConfig[size];

  const SpinnerContent = () => (
    <div className="flex flex-col items-center justify-center space-y-4">
      {/* Spinner Container */}
      <div className={`relative ${config.container} flex items-center justify-center`}>
        {/* Outer Rotating Ring */}
        <div
          className="absolute inset-0 rounded-full border-2 border-transparent animate-spin"
          style={{
            background: 'conic-gradient(from 0deg, #D4AF37, #F4C430, #D4AF37, transparent, transparent)',
            borderRadius: '50%',
            animation: 'spin 2s linear infinite'
          }}
        >
        </div>

        {/* Inner Ring */}
        <div className="absolute inset-1 rounded-full bg-[#1A1A1A] border border-[#D4AF37]/20">
        </div>

        {/* NESA Logo */}
        <div className={`relative ${config.logo} animate-pulse`}>
          <Image
            src="/images/logo/LOGO.svg"
            alt="NESA Loading"
            width={80}
            height={80}
            className="w-full h-full object-contain filter drop-shadow-lg"
            priority
          />
        </div>

        {/* Pulsing Glow Effect */}
        <div className="absolute inset-0 rounded-full bg-[#D4AF37]/10 animate-ping"></div>
      </div>

      {/* Loading Message */}
      {showMessage && (
        <div className="text-center animate-fade-in">
          <p className={`${config.text} font-medium text-[#D4AF37] animate-pulse`}>
            {message}
          </p>
          <div className="flex justify-center space-x-1 mt-2">
            <div className="w-2 h-2 bg-[#D4AF37] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-[#D4AF37] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-[#D4AF37] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        </div>
      )}
    </div>
  );

  // Render based on variant
  switch (variant) {
    case 'overlay':
      return (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center">
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/60 backdrop-blur-md animate-fade-in"></div>

          {/* Spinner */}
          <div className="relative z-10 animate-scale-in">
            <SpinnerContent />
          </div>
        </div>
      );

    case 'page':
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#1A1A1A] via-[#2D1810] to-[#1A1A1A]">
          <div className="animate-fade-in">
            <SpinnerContent />
          </div>
        </div>
      );

    default:
      return (
        <div className="flex items-center justify-center p-4">
          <SpinnerContent />
        </div>
      );
  }
}

// Custom CSS animations (add to globals.css)
export const loadingAnimations = `
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}
`;

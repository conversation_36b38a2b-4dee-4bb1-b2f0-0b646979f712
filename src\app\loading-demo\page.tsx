'use client';

import React, { useState } from 'react';
import { LoadingSpinner, PageLoader } from '../../components/ui';
import { useLoading } from '../../contexts/LoadingContext';

/**
 * Loading Demo Page
 *
 * Demonstrates all the different loading states and components:
 * - Different spinner sizes
 * - Overlay loading
 * - Page loading
 * - Global loading context
 * - Custom messages
 */
export default function LoadingDemoPage(): React.JSX.Element {
  const [showOverlay, setShowOverlay] = useState(false);
  const [showPageLoader, setShowPageLoader] = useState(false);
  const { showLoading, hideLoading, isLoading } = useLoading();

  const handleOverlayDemo = () => {
    setShowOverlay(true);
    setTimeout(() => setShowOverlay(false), 3000);
  };

  const handlePageLoaderDemo = () => {
    setShowPageLoader(true);
    setTimeout(() => setShowPageLoader(false), 4000);
  };

  const handleGlobalLoadingDemo = () => {
    showLoading('Processing your request...');
    setTimeout(() => hideLoading(), 3000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#1A1A1A] via-[#2D1810] to-[#1A1A1A] py-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#D4AF37] mb-4">
            NESA Loading Components
          </h1>
          <p className="text-lg text-white/70 max-w-2xl mx-auto">
            Showcase of beautiful loading states using the NESA logo with premium animations
          </p>
        </div>

        {/* Spinner Sizes Demo */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-[#D4AF37] mb-8 text-center">
            Spinner Sizes
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
              <h3 className="text-white mb-4 font-medium">Small</h3>
              <LoadingSpinner size="sm" message="Loading..." />
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
              <h3 className="text-white mb-4 font-medium">Medium</h3>
              <LoadingSpinner size="md" message="Loading..." />
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
              <h3 className="text-white mb-4 font-medium">Large</h3>
              <LoadingSpinner size="lg" message="Loading..." />
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
              <h3 className="text-white mb-4 font-medium">Extra Large</h3>
              <LoadingSpinner size="xl" message="Loading..." />
            </div>
          </div>
        </section>

        {/* Interactive Demos */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-[#D4AF37] mb-8 text-center">
            Interactive Demos
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Overlay Demo */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
              <h3 className="text-white mb-4 font-medium">Overlay Loading</h3>
              <p className="text-white/60 text-sm mb-6">
                Full-screen overlay with blur background
              </p>
              <button
                onClick={handleOverlayDemo}
                className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
              >
                Show Overlay
              </button>
            </div>

            {/* Page Loader Demo */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
              <h3 className="text-white mb-4 font-medium">Page Loader</h3>
              <p className="text-white/60 text-sm mb-6">
                Full-page loading with animated background
              </p>
              <button
                onClick={handlePageLoaderDemo}
                className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
              >
                Show Page Loader
              </button>
            </div>

            {/* Global Loading Demo */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
              <h3 className="text-white mb-4 font-medium">Global Loading</h3>
              <p className="text-white/60 text-sm mb-6">
                Context-based global loading state
              </p>
              <button
                onClick={handleGlobalLoadingDemo}
                className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Show Global Loading'}
              </button>
            </div>
          </div>
        </section>

        {/* Features List */}
        <section className="text-center">
          <h2 className="text-2xl font-bold text-[#D4AF37] mb-8">
            Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {[
              'Rotating NESA logo animation',
              'Golden gradient ring effects',
              'Pulsing glow animations',
              'Multiple size variants',
              'Custom loading messages',
              'Overlay and page variants',
              'Smooth fade transitions',
              'Backdrop blur effects',
              'Progress bar animations',
              'Context-based state management',
              'TypeScript support',
              'Responsive design'
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-[#D4AF37]/20 text-white/80 text-sm"
              >
                ✨ {feature}
              </div>
            ))}
          </div>
        </section>
      </div>

      {/* Overlay Demo */}
      {showOverlay && (
        <LoadingSpinner
          variant="overlay"
          size="xl"
          message="Loading overlay demo..."
        />
      )}

      {/* Page Loader Demo */}
      <PageLoader
        isLoading={showPageLoader}
        message="Loading page demo..."
      >
        <div></div>
      </PageLoader>
    </div>
  );
}

'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import LoadingSpinner from '../components/ui/LoadingSpinner';

interface LoadingContextType {
  isLoading: boolean;
  loadingMessage: string;
  showLoading: (message?: string) => void;
  hideLoading: () => void;
  setLoadingMessage: (message: string) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

interface LoadingProviderProps {
  children: ReactNode;
}

/**
 * Loading Context Provider
 *
 * Provides global loading state management for the entire application.
 * Features:
 * - Global loading state
 * - Custom loading messages
 * - Easy show/hide methods
 * - TypeScript support
 *
 * @param children - Child components
 * @returns The loading context provider
 */
export function LoadingProvider({ children }: LoadingProviderProps): React.JSX.Element {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Loading...');

  const showLoading = useCallback((message = 'Loading...') => {
    setLoadingMessage(message);
    setIsLoading(true);
  }, []);

  const hideLoading = useCallback(() => {
    setIsLoading(false);
  }, []);

  const updateLoadingMessage = useCallback((message: string) => {
    setLoadingMessage(message);
  }, []);

  const value: LoadingContextType = {
    isLoading,
    loadingMessage,
    showLoading,
    hideLoading,
    setLoadingMessage: updateLoadingMessage,
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
}

/**
 * Hook to use loading context
 *
 * @returns Loading context methods and state
 * @throws Error if used outside LoadingProvider
 */
export function useLoading(): LoadingContextType {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}

/**
 * Higher-order component for automatic loading states
 *
 * @param WrappedComponent - Component to wrap with loading
 * @param loadingMessage - Custom loading message
 * @returns Component with automatic loading state
 */
export function withLoading<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  loadingMessage = 'Loading...'
) {
  return function LoadingWrapper(props: P) {
    const [isComponentLoading, setIsComponentLoading] = useState(true);

    React.useEffect(() => {
      // Simulate component loading
      const timer = setTimeout(() => {
        setIsComponentLoading(false);
      }, 1000);

      return () => clearTimeout(timer);
    }, []);

    if (isComponentLoading) {
      return (
        <div className="flex items-center justify-center min-h-[200px]">
          <LoadingSpinner size="lg" message={loadingMessage} />
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };
}

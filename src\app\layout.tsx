import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Navbar, BackgroundWrapper, GlobalLoadingProvider } from "@/components";
import { LoadingProvider } from "@/contexts/LoadingContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "NESA",
  description: "Honoring Africa's Changemakers & Building the Future of Education Across the Continent",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <GlobalLoadingProvider>
          <LoadingProvider>
            <BackgroundWrapper>
              <Navbar />
              <main className="pt-16 lg:pt-20">
                {children}
              </main>
            </BackgroundWrapper>
          </LoadingProvider>
        </GlobalLoadingProvider>
      </body>
    </html>
  );
}

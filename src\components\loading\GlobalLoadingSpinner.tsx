'use client';

import React from 'react';
import Image from 'next/image';

interface GlobalLoadingSpinnerProps {
  isVisible?: boolean;
}

/**
 * Global Loading Spinner Component
 *
 * Premium loading experience with:
 * - NESA branded spinner with rotating golden ring
 * - Sophisticated fade in/out transitions
 * - Full-screen overlay with luxury timing
 * - Enhanced visual depth and refinement
 *
 * Controlled by GlobalLoadingProvider for consistent state management
 */
export default function GlobalLoadingSpinner({
  isVisible = true
}: GlobalLoadingSpinnerProps): React.JSX.Element {

  return (
    <div
      className={`fixed inset-0 z-[10000] transition-all duration-800 ease-out ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
      style={{
        background: 'radial-gradient(ellipse at center, rgba(26, 26, 26, 0.99) 0%, rgba(45, 24, 16, 0.995) 100%)',
        backdropFilter: 'blur(24px) saturate(1.3)'
      }}
    >
      {/* Enhanced Ambient Glow */}
      <div
        className="absolute inset-0 opacity-25"
        style={{
          background: 'radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.12) 0%, rgba(244, 196, 48, 0.06) 40%, transparent 75%)',
        }}
      />

      {/* Subtle Texture Overlay */}
      <div
        className="absolute inset-0 opacity-[0.02]"
        style={{
          backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(212, 175, 55, 0.3) 1px, transparent 0)',
          backgroundSize: '24px 24px'
        }}
      />

      {/* Main Loading Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen">
        <div className="text-center animate-fade-in-up">

          {/* Enhanced NESA Logo Spinner */}
          <div className="relative w-28 h-28 mx-auto mb-10">
            {/* Outer Glow Ring */}
            <div
              className="absolute inset-0 rounded-full opacity-40"
              style={{
                background: 'conic-gradient(from 0deg, transparent 0%, transparent 70%, rgba(212, 175, 55, 0.6) 85%, rgba(244, 196, 48, 0.8) 95%, transparent 100%)',
                animation: 'spin 3s linear infinite',
                filter: 'blur(2px)',
                transform: 'scale(1.1)'
              }}
            />

            {/* Main Elegant Rotating Ring */}
            <div
              className="absolute inset-0 rounded-full opacity-95"
              style={{
                background: 'conic-gradient(from 0deg, transparent 0%, transparent 70%, #D4AF37 85%, #F4C430 95%, transparent 100%)',
                animation: 'spin 3s linear infinite',
                filter: 'blur(0.3px)'
              }}
            />

            {/* Inner Container with Enhanced Gradient */}
            <div
              className="absolute inset-1 rounded-full"
              style={{
                background: 'radial-gradient(circle at 30% 30%, rgba(45, 24, 16, 0.95) 0%, rgba(26, 26, 26, 0.98) 70%)',
                boxShadow: 'inset 0 0 20px rgba(0, 0, 0, 0.5)'
              }}
            />

            {/* NESA Logo - Static and Elegant */}
            <div className="absolute inset-5">
              <Image
                src="/images/logo/LOGO.svg"
                alt="NESA Loading"
                width={72}
                height={72}
                className="w-full h-full object-contain opacity-96 filter drop-shadow-sm"
                priority
              />
            </div>
          </div>

          {/* Enhanced Minimal Branding */}
          <div className="space-y-4">
            <h1 className="text-2xl font-light text-[#D4AF37] tracking-[0.25em] opacity-92">
              NESA
            </h1>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-6 h-px bg-gradient-to-r from-transparent via-[#D4AF37] to-transparent opacity-50" />
              <div className="w-1 h-1 bg-[#D4AF37] rounded-full opacity-60" />
              <div className="w-6 h-px bg-gradient-to-r from-transparent via-[#D4AF37] to-transparent opacity-50" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Refined loading styles for the new minimalist design
export const globalLoadingStyles = `
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out;
}
`;

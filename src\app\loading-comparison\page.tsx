'use client';

import React, { useState } from 'react';
import Image from 'next/image';

/**
 * Loading Design Comparison Page
 *
 * Showcases the new refined loading design alongside the previous version
 * to demonstrate the improved aesthetics and minimalist approach.
 */
export default function LoadingComparisonPage(): React.JSX.Element {
  const [showOldDesign, setShowOldDesign] = useState(false);
  const [showNewDesign, setShowNewDesign] = useState(false);

  const showOldDemo = () => {
    setShowOldDesign(true);
    setTimeout(() => setShowOldDesign(false), 4000);
  };

  const showNewDemo = () => {
    setShowNewDesign(true);
    setTimeout(() => setShowNewDesign(false), 4000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#1A1A1A] via-[#2D1810] to-[#1A1A1A] py-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#D4AF37] mb-4">
            Loading Design Evolution
          </h1>
          <p className="text-lg text-white/70 max-w-3xl mx-auto">
            From complex to refined: Experience the transformation to minimalist elegance
          </p>
        </div>

        {/* Design Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">

          {/* New Refined Design */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-[#D4AF37]/20">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-[#D4AF37] mb-2">
                ✨ New Refined Design
              </h2>
              <p className="text-white/60 text-sm">
                Minimalist • Elegant • Sophisticated
              </p>
            </div>

            {/* New Design Preview */}
            <div className="bg-black/30 rounded-xl p-8 mb-6 min-h-[300px] flex items-center justify-center">
              <div className="text-center">
                {/* Enhanced NESA Logo Spinner */}
                <div className="relative w-28 h-28 mx-auto mb-10">
                  {/* Outer Glow Ring */}
                  <div
                    className="absolute inset-0 rounded-full opacity-40"
                    style={{
                      background: 'conic-gradient(from 0deg, transparent 0%, transparent 70%, rgba(212, 175, 55, 0.6) 85%, rgba(244, 196, 48, 0.8) 95%, transparent 100%)',
                      animation: 'spin 3s linear infinite',
                      filter: 'blur(2px)',
                      transform: 'scale(1.1)'
                    }}
                  />

                  {/* Main Elegant Rotating Ring */}
                  <div
                    className="absolute inset-0 rounded-full opacity-95"
                    style={{
                      background: 'conic-gradient(from 0deg, transparent 0%, transparent 70%, #D4AF37 85%, #F4C430 95%, transparent 100%)',
                      animation: 'spin 3s linear infinite',
                      filter: 'blur(0.3px)'
                    }}
                  />

                  {/* Inner Container with Enhanced Gradient */}
                  <div
                    className="absolute inset-1 rounded-full"
                    style={{
                      background: 'radial-gradient(circle at 30% 30%, rgba(45, 24, 16, 0.95) 0%, rgba(26, 26, 26, 0.98) 70%)',
                      boxShadow: 'inset 0 0 20px rgba(0, 0, 0, 0.5)'
                    }}
                  />

                  {/* NESA Logo - Static and Elegant */}
                  <div className="absolute inset-5">
                    <Image
                      src="/images/logo/LOGO.svg"
                      alt="NESA Loading"
                      width={72}
                      height={72}
                      className="w-full h-full object-contain opacity-96 filter drop-shadow-sm"
                      priority
                    />
                  </div>
                </div>

                {/* Enhanced Minimal Branding */}
                <div className="space-y-4">
                  <h1 className="text-2xl font-light text-[#D4AF37] tracking-[0.25em] opacity-92">
                    NESA
                  </h1>
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-6 h-px bg-gradient-to-r from-transparent via-[#D4AF37] to-transparent opacity-50" />
                    <div className="w-1 h-1 bg-[#D4AF37] rounded-full opacity-60" />
                    <div className="w-6 h-px bg-gradient-to-r from-transparent via-[#D4AF37] to-transparent opacity-50" />
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={showNewDemo}
                className="w-full bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
              >
                Preview New Design
              </button>

              <div className="text-xs text-white/50 space-y-1">
                <p>• Single rotating ring animation</p>
                <p>• Static, elegant logo presentation</p>
                <p>• Minimal text and clean typography</p>
                <p>• Subtle ambient background glow</p>
                <p>• Refined color gradients</p>
              </div>
            </div>
          </div>

          {/* Old Complex Design */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white/70 mb-2">
                📊 Previous Design
              </h2>
              <p className="text-white/50 text-sm">
                Complex • Multiple Elements • Busy
              </p>
            </div>

            {/* Old Design Preview */}
            <div className="bg-black/30 rounded-xl p-8 mb-6 min-h-[300px] flex items-center justify-center">
              <div className="text-center space-y-6">
                {/* Old Complex Spinner */}
                <div className="relative w-32 h-32 mx-auto">
                  <div
                    className="absolute inset-0 rounded-full"
                    style={{
                      background: 'conic-gradient(from 0deg, #D4AF37 0%, #F4C430 25%, #D4AF37 50%, transparent 75%, transparent 100%)',
                      animation: 'spin 2s linear infinite',
                      filter: 'drop-shadow(0 0 20px rgba(212, 175, 55, 0.3))'
                    }}
                  />
                  <div className="absolute inset-2 rounded-full bg-[#1A1A1A] border-2 border-[#D4AF37]/30" />
                  <div className="absolute inset-6 animate-pulse">
                    <Image
                      src="/images/logo/LOGO.svg"
                      alt="NESA Loading"
                      width={80}
                      height={80}
                      className="w-full h-full object-contain filter drop-shadow-lg"
                      priority
                    />
                  </div>
                  <div className="absolute inset-0 rounded-full bg-[#D4AF37]/20 animate-ping" />
                </div>

                {/* Multiple Text Elements */}
                <div className="space-y-2">
                  <h1 className="text-2xl font-bold text-[#D4AF37] animate-pulse">NESA</h1>
                  <p className="text-sm text-white/80">New Education Standard Award</p>
                  <p className="text-xs text-white/60">Loading your experience...</p>
                </div>

                {/* Bouncing Dots */}
                <div className="flex justify-center space-x-2">
                  <div className="w-2 h-2 bg-[#D4AF37] rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-[#D4AF37] rounded-full animate-bounce" style={{ animationDelay: '200ms' }} />
                  <div className="w-2 h-2 bg-[#D4AF37] rounded-full animate-bounce" style={{ animationDelay: '400ms' }} />
                </div>

                {/* Progress Bar */}
                <div className="w-48 mx-auto">
                  <div className="h-1 bg-white/10 rounded-full overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-[#D4AF37] to-[#F4C430] rounded-full animate-progress" />
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={showOldDemo}
                className="w-full bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200"
              >
                Preview Old Design
              </button>

              <div className="text-xs text-white/40 space-y-1">
                <p>• Multiple competing animations</p>
                <p>• Pulsing logo + rotating ring</p>
                <p>• Bouncing dots + progress bar</p>
                <p>• Multiple text elements</p>
                <p>• Complex background patterns</p>
              </div>
            </div>
          </div>
        </div>

        {/* Design Philosophy */}
        <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-[#D4AF37]/20 text-center">
          <h2 className="text-2xl font-bold text-[#D4AF37] mb-6">
            Design Philosophy: Less is More
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="w-12 h-12 bg-[#D4AF37]/20 rounded-full flex items-center justify-center mx-auto">
                <span className="text-[#D4AF37] text-xl">✨</span>
              </div>
              <h3 className="font-semibold text-white">Simplicity</h3>
              <p className="text-white/60 text-sm">
                Removed visual clutter and competing elements for a clean, focused experience
              </p>
            </div>
            <div className="space-y-3">
              <div className="w-12 h-12 bg-[#D4AF37]/20 rounded-full flex items-center justify-center mx-auto">
                <span className="text-[#D4AF37] text-xl">🎯</span>
              </div>
              <h3 className="font-semibold text-white">Focus</h3>
              <p className="text-white/60 text-sm">
                Single rotating animation draws attention without overwhelming the user
              </p>
            </div>
            <div className="space-y-3">
              <div className="w-12 h-12 bg-[#D4AF37]/20 rounded-full flex items-center justify-center mx-auto">
                <span className="text-[#D4AF37] text-xl">💎</span>
              </div>
              <h3 className="font-semibold text-white">Elegance</h3>
              <p className="text-white/60 text-sm">
                Refined aesthetics that convey premium quality and sophistication
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Full Screen Demos */}
      {showNewDesign && (
        <div className="fixed inset-0 z-[10000] transition-all duration-800 ease-out opacity-100"
             style={{
               background: 'radial-gradient(ellipse at center, rgba(26, 26, 26, 0.99) 0%, rgba(45, 24, 16, 0.995) 100%)',
               backdropFilter: 'blur(24px) saturate(1.3)'
             }}>
          <div className="absolute inset-0 opacity-25"
               style={{ background: 'radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.12) 0%, rgba(244, 196, 48, 0.06) 40%, transparent 75%)' }} />
          <div className="absolute inset-0 opacity-[0.02]"
               style={{
                 backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(212, 175, 55, 0.3) 1px, transparent 0)',
                 backgroundSize: '24px 24px'
               }} />
          <div className="relative z-10 flex items-center justify-center min-h-screen">
            <div className="text-center animate-fade-in-up">
              <div className="relative w-28 h-28 mx-auto mb-10">
                <div className="absolute inset-0 rounded-full opacity-40"
                     style={{
                       background: 'conic-gradient(from 0deg, transparent 0%, transparent 70%, rgba(212, 175, 55, 0.6) 85%, rgba(244, 196, 48, 0.8) 95%, transparent 100%)',
                       animation: 'spin 3s linear infinite',
                       filter: 'blur(2px)',
                       transform: 'scale(1.1)'
                     }} />
                <div className="absolute inset-0 rounded-full opacity-95"
                     style={{
                       background: 'conic-gradient(from 0deg, transparent 0%, transparent 70%, #D4AF37 85%, #F4C430 95%, transparent 100%)',
                       animation: 'spin 3s linear infinite',
                       filter: 'blur(0.3px)'
                     }} />
                <div className="absolute inset-1 rounded-full"
                     style={{
                       background: 'radial-gradient(circle at 30% 30%, rgba(45, 24, 16, 0.95) 0%, rgba(26, 26, 26, 0.98) 70%)',
                       boxShadow: 'inset 0 0 20px rgba(0, 0, 0, 0.5)'
                     }} />
                <div className="absolute inset-5">
                  <Image src="/images/logo/LOGO.svg" alt="NESA Loading" width={72} height={72} className="w-full h-full object-contain opacity-96 filter drop-shadow-sm" priority />
                </div>
              </div>
              <div className="space-y-4">
                <h1 className="text-2xl font-light text-[#D4AF37] tracking-[0.25em] opacity-92">NESA</h1>
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-6 h-px bg-gradient-to-r from-transparent via-[#D4AF37] to-transparent opacity-50" />
                  <div className="w-1 h-1 bg-[#D4AF37] rounded-full opacity-60" />
                  <div className="w-6 h-px bg-gradient-to-r from-transparent via-[#D4AF37] to-transparent opacity-50" />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {showOldDesign && (
        <div className="fixed inset-0 z-[10000] bg-gradient-to-br from-[#1A1A1A] via-[#2D1810] to-[#1A1A1A]">
          <div className="flex items-center justify-center min-h-screen p-8">
            <div className="text-center space-y-8">
              <div className="relative w-32 h-32 mx-auto">
                <div className="absolute inset-0 rounded-full"
                     style={{
                       background: 'conic-gradient(from 0deg, #D4AF37 0%, #F4C430 25%, #D4AF37 50%, transparent 75%, transparent 100%)',
                       animation: 'spin 2s linear infinite',
                       filter: 'drop-shadow(0 0 20px rgba(212, 175, 55, 0.3))'
                     }} />
                <div className="absolute inset-2 rounded-full bg-[#1A1A1A] border-2 border-[#D4AF37]/30" />
                <div className="absolute inset-6 animate-pulse">
                  <Image src="/images/logo/LOGO.svg" alt="NESA Loading" width={80} height={80} className="w-full h-full object-contain filter drop-shadow-lg" priority />
                </div>
                <div className="absolute inset-0 rounded-full bg-[#D4AF37]/20 animate-ping" />
              </div>
              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-[#D4AF37] animate-pulse">NESA</h1>
                <p className="text-lg text-white/80">New Education Standard Award</p>
                <p className="text-sm text-white/60">Loading your experience...</p>
              </div>
              <div className="flex justify-center space-x-2">
                <div className="w-3 h-3 bg-[#D4AF37] rounded-full animate-bounce" />
                <div className="w-3 h-3 bg-[#D4AF37] rounded-full animate-bounce" style={{ animationDelay: '200ms' }} />
                <div className="w-3 h-3 bg-[#D4AF37] rounded-full animate-bounce" style={{ animationDelay: '400ms' }} />
              </div>
              <div className="w-64 mx-auto">
                <div className="h-1 bg-white/10 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-[#D4AF37] to-[#F4C430] rounded-full animate-progress" />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

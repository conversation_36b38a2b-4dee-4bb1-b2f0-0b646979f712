'use client';

import React, { useState } from 'react';
import { LoadingSpinner } from '@/components/ui';
import { useLoading } from '@/contexts/LoadingContext';

export default function Home() {
  const [showSpinner, setShowSpinner] = useState(false);
  const { showLoading, hideLoading, isLoading } = useLoading();

  const handleTestSpinner = () => {
    setShowSpinner(true);
    setTimeout(() => setShowSpinner(false), 3000);
  };

  const handleTestGlobalLoading = () => {
    showLoading('Testing global loading...');
    setTimeout(() => hideLoading(), 3000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#1A1A1A] via-[#2D1810] to-[#1A1A1A] py-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#D4AF37] mb-4">
            Welcome to NESA
          </h1>
          <p className="text-lg text-white/70 max-w-2xl mx-auto">
            New Education Standard Award - Honoring Africa's Changemakers
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {/* Loading Spinner Test */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
            <h2 className="text-xl font-bold text-[#D4AF37] mb-4">
              Test Loading Spinner
            </h2>
            <p className="text-white/60 text-sm mb-6">
              Click to see the NESA loading spinner in action
            </p>
            {showSpinner ? (
              <LoadingSpinner size="lg" message="Loading NESA..." />
            ) : (
              <button
                onClick={handleTestSpinner}
                className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
              >
                Show Spinner
              </button>
            )}
          </div>

          {/* Global Loading Test */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 text-center border border-[#D4AF37]/20">
            <h2 className="text-xl font-bold text-[#D4AF37] mb-4">
              Test Global Loading
            </h2>
            <p className="text-white/60 text-sm mb-6">
              Click to test the global loading overlay
            </p>
            <button
              onClick={handleTestGlobalLoading}
              disabled={isLoading}
              className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105 disabled:opacity-50"
            >
              {isLoading ? 'Loading...' : 'Show Global Loading'}
            </button>
          </div>
        </div>

        {/* Spinner Sizes Demo */}
        <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-[#D4AF37]/20">
          <h2 className="text-xl font-bold text-[#D4AF37] mb-6 text-center">
            Loading Spinner Sizes
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-white/60 text-sm mb-4">Small</p>
              <LoadingSpinner size="sm" showMessage={false} />
            </div>
            <div className="text-center">
              <p className="text-white/60 text-sm mb-4">Medium</p>
              <LoadingSpinner size="md" showMessage={false} />
            </div>
            <div className="text-center">
              <p className="text-white/60 text-sm mb-4">Large</p>
              <LoadingSpinner size="lg" showMessage={false} />
            </div>
            <div className="text-center">
              <p className="text-white/60 text-sm mb-4">Extra Large</p>
              <LoadingSpinner size="xl" showMessage={false} />
            </div>
          </div>
        </div>

        <div className="text-center mt-12 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/loading-demo"
              className="inline-flex items-center px-6 py-3 bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] rounded-xl font-medium transition-all duration-200 hover:scale-105"
            >
              View Loading Demo →
            </a>
            <a
              href="/test-loading"
              className="inline-flex items-center px-6 py-3 bg-[#D4AF37]/20 hover:bg-[#D4AF37] text-[#D4AF37] hover:text-[#1A1A1A] border border-[#D4AF37] rounded-xl font-medium transition-all duration-200 hover:scale-105"
            >
              Test Global Loading →
            </a>
            <a
              href="/loading-comparison"
              className="inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white border border-white/20 rounded-xl font-medium transition-all duration-200 hover:scale-105"
            >
              ✨ New Design →
            </a>
          </div>
          <p className="text-white/50 text-sm">
            Try refreshing the page to see the new refined loading spinner!
          </p>
        </div>
      </div>
    </div>
  );
}

'use client';

import React from 'react';
import Link from 'next/link';
import { useLoadingControl } from '@/components/loading';
import LoadingDebugger from '@/components/loading/LoadingDebugger';

/**
 * Test Loading Page
 *
 * This page demonstrates the global loading functionality:
 * - Automatic loading on page refresh
 * - Route transition loading
 * - Manual loading control
 */
export default function TestLoadingPage(): React.JSX.Element {
  const { showLoading, hideLoading, withLoading } = useLoadingControl();

  const handleManualLoading = () => {
    showLoading();
    setTimeout(() => {
      hideLoading();
    }, 3000);
  };

  const handleAsyncOperation = async () => {
    await withLoading(async () => {
      // Simulate an async operation
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('Operation completed!');
    });
  };

  const simulatePageRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#1A1A1A] via-[#2D1810] to-[#1A1A1A] py-20">
      <LoadingDebugger enabled={true} />
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#D4AF37] mb-4">
            Global Loading Test
          </h1>
          <p className="text-lg text-white/70 max-w-2xl mx-auto">
            Test the automatic global loading spinner functionality
          </p>
        </div>

        {/* Test Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">

          {/* Automatic Loading Tests */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-[#D4AF37]/20">
            <h2 className="text-xl font-bold text-[#D4AF37] mb-6">
              Automatic Loading
            </h2>
            <div className="space-y-4">
              <div className="p-4 bg-white/5 rounded-xl">
                <h3 className="font-medium text-white mb-2">Page Refresh</h3>
                <p className="text-white/60 text-sm mb-4">
                  Refresh the page to see the automatic loading spinner
                </p>
                <button
                  onClick={simulatePageRefresh}
                  className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105"
                >
                  Refresh Page
                </button>
              </div>

              <div className="p-4 bg-white/5 rounded-xl">
                <h3 className="font-medium text-white mb-2">Route Navigation</h3>
                <p className="text-white/60 text-sm mb-4">
                  Navigate between pages to see route transition loading
                </p>
                <div className="space-x-2">
                  <Link
                    href="/"
                    className="inline-block bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105"
                  >
                    Go Home
                  </Link>
                  <Link
                    href="/loading-demo"
                    className="inline-block bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105"
                  >
                    Loading Demo
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Manual Loading Tests */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-[#D4AF37]/20">
            <h2 className="text-xl font-bold text-[#D4AF37] mb-6">
              Manual Loading Control
            </h2>
            <div className="space-y-4">
              <div className="p-4 bg-white/5 rounded-xl">
                <h3 className="font-medium text-white mb-2">Manual Trigger</h3>
                <p className="text-white/60 text-sm mb-4">
                  Manually show/hide the global loading spinner
                </p>
                <button
                  onClick={handleManualLoading}
                  className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105"
                >
                  Show Loading (3s)
                </button>
              </div>

              <div className="p-4 bg-white/5 rounded-xl">
                <h3 className="font-medium text-white mb-2">Async Operation</h3>
                <p className="text-white/60 text-sm mb-4">
                  Test loading with async operations
                </p>
                <button
                  onClick={handleAsyncOperation}
                  className="bg-[#D4AF37] hover:bg-[#F4C430] text-[#1A1A1A] px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105"
                >
                  Run Async Operation
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-[#D4AF37]/20">
          <h2 className="text-xl font-bold text-[#D4AF37] mb-6 text-center">
            Global Loading Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              'Automatic page refresh detection',
              'Route transition loading',
              'Initial page load spinner',
              'NESA branded animations',
              'Smooth fade transitions',
              'Manual loading control',
              'Async operation support',
              'High z-index overlay',
              'Responsive design',
              'Performance optimized',
              'No manual setup required',
              'Global state management'
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-[#D4AF37]/10 text-white/80 text-sm text-center"
              >
                ✨ {feature}
              </div>
            ))}
          </div>
        </div>

        {/* Premium Loading Information */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-[#D4AF37]/10 border border-[#D4AF37]/30 rounded-xl p-6">
            <h3 className="text-lg font-bold text-[#D4AF37] mb-3">
              Premium Loading Timing
            </h3>
            <div className="text-white/70 space-y-2 text-sm">
              <p><strong>Initial Load:</strong> 1.8 seconds minimum display</p>
              <p><strong>Route Changes:</strong> 1.2 seconds for transitions</p>
              <p><strong>Manual Loading:</strong> 1.8 seconds for operations</p>
              <p><strong>Fade Out:</strong> 0.8 seconds smooth transition</p>
              <p className="text-[#D4AF37] text-xs mt-3">
                ✨ Designed for premium, luxury user experience
              </p>
            </div>
          </div>

          <div className="bg-white/5 border border-white/20 rounded-xl p-6">
            <h3 className="text-lg font-bold text-white mb-3">
              How to Test
            </h3>
            <div className="text-white/70 space-y-2 text-sm">
              <p>1. <strong>Page Refresh:</strong> Press F5 or Ctrl+R</p>
              <p>2. <strong>Route Navigation:</strong> Click navigation links</p>
              <p>3. <strong>Manual Control:</strong> Use buttons above</p>
              <p>4. <strong>Debug Mode:</strong> Click debug button (top-right)</p>
              <p className="text-blue-300 text-xs mt-3">
                🔍 Debug mode shows real-time loading events
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
